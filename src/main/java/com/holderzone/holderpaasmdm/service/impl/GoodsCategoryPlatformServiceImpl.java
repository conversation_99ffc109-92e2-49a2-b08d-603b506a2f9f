package com.holderzone.holderpaasmdm.service.impl;

import com.holderzone.holderpaasmdm.common.exception.StoreBaseException;
import com.holderzone.holderpaasmdm.enumeraton.ResponseCode;
import com.holderzone.holderpaasmdm.mapper.service.GoodsCategoryPlatformMapperService;
import com.holderzone.holderpaasmdm.mapper.service.StoreGoodsMapperService;
import com.holderzone.holderpaasmdm.mapper.service.StoreSalesProgramGoodsMapperService;
import com.holderzone.holderpaasmdm.mapper.service.StoreSalesProgramMapperService;
import com.holderzone.holderpaasmdm.model.dto.QueryCategoryPlatformPageDTO;
import com.holderzone.holderpaasmdm.model.dto.QueryCategoryPlatformTreeDTO;
import com.holderzone.holderpaasmdm.model.po.GoodsCategoryPlatformPO;
import com.holderzone.holderpaasmdm.model.po.StoreGoodsPO;
import com.holderzone.holderpaasmdm.model.po.StoreSalesProgramGoodsPO;
import com.holderzone.holderpaasmdm.model.po.StoreSalesProgramPO;
import com.holderzone.holderpaasmdm.model.vo.GoodsCategoryPlatformTreeVO;
import com.holderzone.holderpaasmdm.model.vo.GoodsPictureVO;
import com.holderzone.holderpaasmdm.model.vo.PageRespVO;
import com.holderzone.holderpaasmdm.model.vo.StoreGoodsExtendChannelVO;
import com.holderzone.holderpaasmdm.service.GoodsCategoryPlatformService;
import com.holderzone.holderpaasmdm.service.UploadFilesService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 平台类目信息
 *
 * <AUTHOR>
 * @date 2025/6/3 9:38
 **/
@Service
@RequiredArgsConstructor
public class GoodsCategoryPlatformServiceImpl implements GoodsCategoryPlatformService {

    private final GoodsCategoryPlatformMapperService goodsCategoryPlatformMapperService;
    private final StoreGoodsMapperService storeGoodsMapperService;
    private final StoreSalesProgramMapperService storeSalesProgramMapperService;
    private final StoreSalesProgramGoodsMapperService storeSalesProgramGoodsMapperService;
    private final UploadFilesService uploadFilesService;

    @Override
    public List<GoodsCategoryPlatformTreeVO> queryGoodsCategoryPlatformTree(QueryCategoryPlatformTreeDTO queryCategoryPlatformTreeDTO) {
        // 查询所有数据
        List<GoodsCategoryPlatformPO> platformPOList = goodsCategoryPlatformMapperService
                .lambdaQuery()
                .orderByAsc(GoodsCategoryPlatformPO::getSort)
                .list();
        // 查询商品数量
        Map<Integer, Integer> storeGoodsCountMap = findStoreGoodsCount(platformPOList,
                queryCategoryPlatformTreeDTO.getChannelId(), queryCategoryPlatformTreeDTO.getIsQueryGoodsCount());

        // 查询图片信息
        Map<Integer, GoodsPictureVO> pictureInfo = findPictureInfo(platformPOList, queryCategoryPlatformTreeDTO.getIsQueryPicture());
        List<GoodsCategoryPlatformTreeVO> voList = new ArrayList<>();
        // 树结构数据
        if (Boolean.TRUE.equals(queryCategoryPlatformTreeDTO.getIsTree())) {
            // 获取第一级类目
            List<GoodsCategoryPlatformPO> parentPOList = platformPOList.stream()
                    .filter(item -> Objects.isNull(item.getParentId()) || item.getLevel() == 1)
                    .toList();
            // 树形结构展示
            findTreeVO(parentPOList, platformPOList, voList, storeGoodsCountMap, pictureInfo);
        } else {
            // 平铺展示
            for (GoodsCategoryPlatformPO platformPO : platformPOList) {
                voList.add(po2TreeVO(platformPO, storeGoodsCountMap, pictureInfo));
            }
        }
        return voList;
    }

    @Override
    public PageRespVO<GoodsCategoryPlatformTreeVO> queryGoodsCategoryPlatformPage(QueryCategoryPlatformPageDTO pageDTO) {
        // 查询所有数据
        List<GoodsCategoryPlatformPO> platformPOList = goodsCategoryPlatformMapperService
                .lambdaQuery()
                .orderByAsc(GoodsCategoryPlatformPO::getSort)
                .list();
        if (platformPOList.isEmpty()) {
            return new PageRespVO<>();
        }
        // 查询商品数量
        Map<Integer, Integer> storeGoodsCountMap = findStoreGoodsCount(platformPOList, pageDTO.getChannelId(), true);
        // 查询图片信息
        Map<Integer, GoodsPictureVO> pictureInfo = findPictureInfo(platformPOList, pageDTO.getIsQueryPicture());

        List<GoodsCategoryPlatformTreeVO> voList = new ArrayList<>();
        // 查询一级类目，并封装
        for (GoodsCategoryPlatformPO po : platformPOList) {
            if (po.getLevel() == 1) {
                GoodsCategoryPlatformTreeVO treeVO = po2TreeVO(po, storeGoodsCountMap, pictureInfo);
                voList.add(treeVO);
            }
        }
        return new PageRespVO<>(voList, pageDTO.getLimit(), pageDTO.getPage());
    }

    @Override
    public StoreGoodsExtendChannelVO.CategoryLevelVO queryGoodsCategoryPlatform(Integer id) {
        // 查询渠道下所有数据
        List<GoodsCategoryPlatformPO> platformPOList = goodsCategoryPlatformMapperService.list();
        StoreGoodsExtendChannelVO.CategoryLevelVO categoryLevelVO = new StoreGoodsExtendChannelVO.CategoryLevelVO();
        // 封装数据
        setCategoryLevel(categoryLevelVO, platformPOList, id);
        return categoryLevelVO;
    }

    @Override
    public GoodsCategoryPlatformTreeVO findTreeVoById(QueryCategoryPlatformTreeDTO queryCategoryPlatformTreeDTO) {
        // 查询所有数据
        List<GoodsCategoryPlatformPO> platformPOList = goodsCategoryPlatformMapperService
                .lambdaQuery()
                .orderByAsc(GoodsCategoryPlatformPO::getSort)
                .list();
        Optional<GoodsCategoryPlatformPO> poOptional = platformPOList.stream()
                .filter(item ->
                        Objects.equals(queryCategoryPlatformTreeDTO.getCategoryPlatformId(), item.getId()))
                .findFirst();
        if (poOptional.isEmpty()) {
            throw new StoreBaseException(ResponseCode.COMMON_INVALID_PARAMETER, "平台类目数据不存在");
        }
        // 查询图片信息
        Map<Integer, GoodsPictureVO> pictureInfo = findPictureInfo(platformPOList, queryCategoryPlatformTreeDTO.getIsQueryPicture());
        // 递归获取数据
        List<GoodsCategoryPlatformTreeVO> returnList = new ArrayList<>();
        findTreeVO(List.of(poOptional.get()), platformPOList, returnList, Map.of(), pictureInfo);
        return returnList.get(0);
    }

    /**
     * 设置层级数据
     *
     * @param categoryLevelVO 分级对象
     * @param platformPOList  所有类目数据
     * @param id              当前id
     */
    private void setCategoryLevel(StoreGoodsExtendChannelVO.CategoryLevelVO categoryLevelVO,
                                  List<GoodsCategoryPlatformPO> platformPOList, Integer id) {
        // 获取当前数据
        Optional<GoodsCategoryPlatformPO> poOptional = platformPOList.stream()
                .filter(po -> po.getId().equals(id))
                .findFirst();
        if (poOptional.isEmpty()) {
            return;
        }
        GoodsCategoryPlatformPO platformPO = poOptional.get();
        switch (platformPO.getLevel()) {
            case 1 ->
                    categoryLevelVO.setOne(new StoreGoodsExtendChannelVO.IdNameVO(platformPO.getId(), platformPO.getName()));
            case 2 -> {
                categoryLevelVO.setTwo(new StoreGoodsExtendChannelVO.IdNameVO(platformPO.getId(), platformPO.getName()));
                // 查询父级
                setCategoryLevel(categoryLevelVO, platformPOList, platformPO.getParentId());
            }
            case 3 -> {
                categoryLevelVO.setThree(new StoreGoodsExtendChannelVO.IdNameVO(platformPO.getId(), platformPO.getName()));
                // 查询父级
                setCategoryLevel(categoryLevelVO, platformPOList, platformPO.getParentId());
            }
        }
    }

    /**
     * 获取每一层级的商品数量
     *
     * @param platformPOList    品牌列表
     * @param channelId         渠道id
     * @param isQueryGoodsCount 是否查询商品数量
     * @return 查询结果
     */
    private Map<Integer, Integer> findStoreGoodsCount(List<GoodsCategoryPlatformPO> platformPOList, Integer channelId,
                                                      Boolean isQueryGoodsCount) {
        if (!Boolean.TRUE.equals(isQueryGoodsCount)) {
            return Map.of();
        }

        Map<Integer, Long> platformGoodsMap = buildPlatformGoodsMap(platformPOList, channelId);
        if (platformGoodsMap.isEmpty()) {
            return Map.of();
        }

        return calculateGoodsCountForEachCategory(platformPOList, platformGoodsMap);
    }

    /**
     * 构建平台商品映射
     *
     * @param platformPOList 品牌列表
     * @param channelId      渠道id
     * @return 平台商品映射
     */
    private Map<Integer, Long> buildPlatformGoodsMap(List<GoodsCategoryPlatformPO> platformPOList, Integer channelId) {
        // 获取所有类目的id集合
        List<Integer> platformIds = platformPOList.stream()
                .map(GoodsCategoryPlatformPO::getId)
                .toList();
        if (platformIds.isEmpty()) {
            return Map.of();
        }
        // 查询渠道下所有启用的销售策略
        List<Integer> salesProgramIds = storeSalesProgramMapperService.lambdaQuery()
                .eq(StoreSalesProgramPO::getChannelId, channelId)
                .eq(StoreSalesProgramPO::getIsEnable, Boolean.TRUE)
                .list().stream()
                .map(StoreSalesProgramPO::getId)
                .toList();
        if (salesProgramIds.isEmpty()) {
            return Map.of();
        }
        // 查询商品id集合
        List<Integer> goodsIds = storeSalesProgramGoodsMapperService.lambdaQuery()
                .in(StoreSalesProgramGoodsPO::getStoreSalesProgramId, salesProgramIds)
                .list().stream().map(StoreSalesProgramGoodsPO::getGoodsId).toList();
        if (goodsIds.isEmpty()) {
            return Map.of();
        }
        // 查询已上架的商品数据
        List<Integer> saleGoodsIds = storeGoodsMapperService.queryOnSaleIdsByIdIn(goodsIds);
        if (saleGoodsIds.isEmpty()) {
            return Map.of();
        }
        // 查询商品信息
        return storeGoodsMapperService.lambdaQuery()
                .select(StoreGoodsPO::getId, StoreGoodsPO::getCategoryPlatform)
                .in(StoreGoodsPO::getCategoryPlatform, platformIds)
                .in(StoreGoodsPO::getId, saleGoodsIds)
                .list()
                .stream()
                .collect(Collectors.groupingBy(StoreGoodsPO::getCategoryPlatform, Collectors.counting()));
    }

    /**
     * 计算每个分类的商品数量
     *
     * @param platformPOList    品牌列表
     * @param platformGoodsMap  平台商品映射
     * @return 每个分类的商品数量
     */
    private Map<Integer, Integer> calculateGoodsCountForEachCategory(List<GoodsCategoryPlatformPO> platformPOList,
                                                                     Map<Integer, Long> platformGoodsMap) {
        Map<Integer, Integer> returnMap = new HashMap<>();
        for (GoodsCategoryPlatformPO po : platformPOList) {
            Set<Integer> thirdIds = getThirdLevelIds(po, platformPOList);
            if (!thirdIds.isEmpty()) {
                int count = calculateTotalCount(thirdIds, platformGoodsMap);
                returnMap.put(po.getId(), count);
            }
        }
        return returnMap;
    }

    /**
     * 获取第三层级的ID集合
     *
     * @param po             当前分类
     * @param platformPOList 所有分类列表
     * @return 第三层级ID集合
     */
    private Set<Integer> getThirdLevelIds(GoodsCategoryPlatformPO po, List<GoodsCategoryPlatformPO> platformPOList) {
        Set<Integer> ids = new HashSet<>();
        ids.add(po.getId());

        if (po.getLevel() == 1) {
            return findThirdIds(ids, 1, platformPOList);
        } else if (po.getLevel() == 2) {
            return findThirdIds(ids, 2, platformPOList);
        } else {
            // 第三层，直接返回
            return ids;
        }
    }

    /**
     * 计算总数量
     *
     * @param thirdIds         第三层级ID集合
     * @param platformGoodsMap 平台商品映射
     * @return 总数量
     */
    private int calculateTotalCount(Set<Integer> thirdIds, Map<Integer, Long> platformGoodsMap) {
        int count = 0;
        for (Integer id : thirdIds) {
            count += platformGoodsMap.getOrDefault(id, 0L);
        }
        return count;
    }

    /**
     * 获取第三层级的id数据
     *
     * @param ids            id数据
     * @param level          层级
     * @param platformPOList 品牌列表
     * @return 查询结果
     */
    private Set<Integer> findThirdIds(Set<Integer> ids, int level, List<GoodsCategoryPlatformPO> platformPOList) {
        if (level >= 3) {
            return ids;
        }
        // 查询子集类目id
        List<Integer> returnIds = platformPOList.stream()
                .filter(item -> ids.contains(item.getParentId()))
                .map(GoodsCategoryPlatformPO::getId)
                .toList();
        ids.addAll(returnIds);
        level++;
        return findThirdIds(ids, level, platformPOList);
    }

    /**
     * 查询并封装树结构
     *
     * @param allList            所有平台数据
     * @param voList             返回列表数据
     * @param storeGoodsCountMap 商品数量
     * @param pictureInfo        封面图片
     */
    private void findTreeVO(List<GoodsCategoryPlatformPO> thisList, List<GoodsCategoryPlatformPO> allList,
                            List<GoodsCategoryPlatformTreeVO> voList,
                            Map<Integer, Integer> storeGoodsCountMap, Map<Integer, GoodsPictureVO> pictureInfo) {
        for (GoodsCategoryPlatformPO po : thisList) {
            // 封装数据
            GoodsCategoryPlatformTreeVO treeVO = po2TreeVO(po, storeGoodsCountMap, pictureInfo);
            if (po.getLevel() == 1) {
                setChildren(treeVO, 2, allList, storeGoodsCountMap, pictureInfo);
            }
            // 原本是第二层，需要封装查询第三层
            if (po.getLevel() == 2) {
                setChildren(treeVO, 1, allList, storeGoodsCountMap, pictureInfo);
            }
            voList.add(treeVO);
        }
    }

    /**
     * po转GoodsCategoryPlatformTreeVO
     *
     * @param po                 实体
     * @param storeGoodsCountMap 商品个数
     * @param pictureInfo        图片数据
     * @return 查询结果
     */
    private GoodsCategoryPlatformTreeVO po2TreeVO(GoodsCategoryPlatformPO po, Map<Integer, Integer> storeGoodsCountMap,
                                                  Map<Integer, GoodsPictureVO> pictureInfo) {
        GoodsCategoryPlatformTreeVO treeVO = new GoodsCategoryPlatformTreeVO();
        BeanUtils.copyProperties(po, treeVO);
        treeVO.setGoodsCount(storeGoodsCountMap.get(po.getId()));
        treeVO.setPicture(pictureInfo.get(po.getCoverId()));
        return treeVO;
    }

    /**
     * 设置子集数据
     *
     * @param treeVO             当前层级
     * @param downLevel          往下查询层级
     * @param allList            所有数据信息
     * @param storeGoodsCountMap 商品数量
     * @param pictureInfo        图片信息
     */
    private void setChildren(GoodsCategoryPlatformTreeVO treeVO, int downLevel, List<GoodsCategoryPlatformPO> allList,
                             Map<Integer, Integer> storeGoodsCountMap, Map<Integer, GoodsPictureVO> pictureInfo) {
        if (downLevel <= 0) {
            return;
        }
        // 查询子节点
        List<GoodsCategoryPlatformTreeVO> childrenList = allList.stream()
                .filter(item -> Objects.equals(item.getParentId(), treeVO.getId()))
                .map(item -> po2TreeVO(item, storeGoodsCountMap, pictureInfo))
                .toList();
        treeVO.setChildren(childrenList);
        for (GoodsCategoryPlatformTreeVO vo : childrenList) {
            setChildren(vo, downLevel - 1, allList, storeGoodsCountMap, pictureInfo);
        }
    }

    /**
     * 查询图片信息
     *
     * @param platformPOList 分类集合
     * @param isQueryPicture 是否查询封面
     * @return 查询结果
     */
    private Map<Integer, GoodsPictureVO> findPictureInfo(List<GoodsCategoryPlatformPO> platformPOList,
                                                         Boolean isQueryPicture) {
        if (!Boolean.TRUE.equals(isQueryPicture)) {
            return Map.of();
        }
        List<Integer> coverIds = platformPOList.stream()
                .map(GoodsCategoryPlatformPO::getCoverId)
                .filter(Objects::nonNull)
                .toList();
        return uploadFilesService.findFileByIdIn(coverIds);
    }
}
