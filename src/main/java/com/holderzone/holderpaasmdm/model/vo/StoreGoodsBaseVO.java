package com.holderzone.holderpaasmdm.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.holderzone.holderpaasmdm.model.po.GoodsCategoryPlatformPO;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Description: 门店商品基础vo
 * Author: 向超
 * Date: 2024/11/25 15:36
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class StoreGoodsBaseVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @JsonProperty("goods_id")
    private Integer id;

    /**
     * 门店商品名称
     */
    @JsonProperty("goods_sale_name")
    private String goodsName;

    /**
     * 拼音简码
     */
    @JsonProperty("pinyin_code")
    private String pinyinCode;

    /**
     * 门店商品SPU码
     */
    @JsonProperty("spu_code")
    private String spuCode;

    /**
     * 组合类型（单品/套餐）
     * 1：单品，2：套餐
     */
    @JsonProperty("combo_type")
    private Integer comboType;

    /**
     * 组合类型（单品/套餐） 名称
     * 1：单品，2：套餐
     */
    @JsonProperty("combo_type_name")
    private String comboTypeName;

    /**
     * 属性（门店商品特征）
     * 1：可销售，2：可生产，3：可采购
     */
    @JsonProperty("goods_features")
    private List<Integer> goodsFeatures;

    /**
     * 属性（门店商品特征） 名称
     * 1：可销售，2：可生产，3：可采购
     */
    @JsonProperty("goods_features_name")
    private List<String> goodsFeaturesName;

    /**
     * 门店商品类型
     */
    @JsonProperty("goods_type")
    private Integer goodsType;

    /**
     * 计价方式（普通/计数/计重）
     * 1：普通，2：计数，3：计重
     */
    @JsonProperty("valuation_method")
    private Integer valuationMethod;

    /**
     * 计价方式（普通/计数/计重） 名称
     * 1：普通，2：计数，3：计重
     */
    @JsonProperty("valuation_method_name")
    private String valuationMethodName;

    /**
     * 库存属性(单品/称重单品)
     * 1：可库存产品，2：虚拟产品，3：服务类产品
     */
    @JsonProperty("inventory_property")
    private Integer inventoryProperty;

    /**
     * 库存属性(单品/称重单品) 名称
     * 1：可库存产品，2：虚拟产品，3：服务类产品
     */
    @JsonProperty("inventory_property_name")
    private String inventoryPropertyName;

    /**
     * 门店商品分类
     */
    @JsonProperty("store_category")
    private Integer category;

    /**
     * 门店商品单位
     */
    @JsonProperty("goods_unit")
    private Integer goodsUnit;

    /**
     * 门店商品品牌
     */
    @JsonProperty("brand")
    private Integer brand;

    /**
     * 门店商品条码
     */
    @JsonProperty("barcode")
    private String barcode;

    /**
     * 门店商品标签
     */
    @JsonProperty("goods_label")
    private Integer goodsLabel;

    /**
     * 门店商品成本
     */
    @JsonProperty("costs")
    private String costs;

    /**
     * 门店商品售价
     */
    @JsonProperty("selling_price")
    private String sellingPrice;

    /**
     * 供应商
     */
    @JsonProperty("provider")
    private String provider;

    /**
     * 生产日期
     */
    @JsonProperty("production_date")
    private LocalDateTime productionDate;

    /**
     * 保质日期
     */
    @JsonProperty("warranty_date")
    private Integer warrantyDate;

    /**
     * 生产厂家
     */
    @JsonProperty("manufacturer")
    private String manufacturer;

    /**
     * 产地
     */
    @JsonProperty("origin")
    private String origin;

    /**
     * 门店商品介绍
     */
    @JsonProperty("introduction_text")
    private String introductionText;

    /**
     * PLU码，称内码
     */
    @JsonProperty("plu_code")
    private String pluCode;

    /**
     * 商品状态，可选值：1 正常 2 禁用
     */
    @JsonProperty("status")
    private Integer status;

    /**
     * 上下架状态，可选值：1 已上架、2 已下架
     */
    @JsonProperty("listing_status")
    private Integer listingStatus;

    /**
     * 门店商品唯一id，后续其他业务使用
     */
    @JsonProperty("goods_uuid")
    private String goodsUuid;

    /**
     * 企业商品唯一id，后续其他业务使用
     */
    @JsonProperty("related_goods_uuid")
    private String relatedGoodsUuid;

    /**
     * 商品排序
     */
    @JsonProperty("sort")
    private Integer sort;

    /**
     * 门店ID
     */
    @JsonProperty("store_id")
    private Integer storeId;

    /**
     * 企业商品ID
     */
    @JsonProperty("company_goods_id")
    private Integer goodsId;

    /**
     * 门店商品自编码
     */
    @JsonProperty("goods_custom_code")
    private String goodsCustomCode;

    /**
     * 店铺商品封面
     */
    @JsonProperty("cover")
    private Integer cover;

    /**
     * 商品规格：1：单规格 2：多规格
     */
    @JsonProperty("goods_spec")
    private Integer goodsSpec;

    /**
     * 创建时间
     */
    @JsonProperty("created_at")
    private Timestamp createdAt;

    /**
     * 最后修改时间
     */
    @JsonProperty("updated_at")
    private Timestamp updatedAt;

    /**
     * 平台分类id
     */
    @JsonProperty("category_platform")
    private Integer categoryPlatform;
}