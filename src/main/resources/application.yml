server:
  port: 8070
spring:
  data:
    redis:
      host: ${I_REDIS_HOST:***************}
      port: 6379
      database: 0
  application:
    name: holder-paas-mdm
  datasource:
    url: ${POSTGRESQL_NEW_HOST:**************************************}/
    username: ${POSTGRESQL_NEW_USERNAME:market}
    password: ${POSTGRESQL_NEW_PASSWORD:marketpg}
#    url: ${POSTGRESQL_NEW_HOST:**************************************}/
#    username: ${POSTGRESQL_NEW_USERNAME:market}
#    password: ${POSTGRESQL_NEW_PASSWORD:marketre}
    driver-class-name: org.postgresql.Driver
    type: com.zaxxer.hikari.HikariDataSource
    maximum-pool-size: 30
    minimum-idle: 5
    connection-timeout: 30000
    idle-timeout: 600000
    max-lifetime: 1800000
mybatis-plus:
  type-aliases-package: com.holderzone.holderpaasmdm.model.po
  global-config:
    db-config:
      logic-delete-field: disabled
      logic-delete-value: true
      logic-not-delete-value: false

logging:
  level:
    root: INFO

management:
  health:
    db:
      enabled: false

# 出库 扣减库存接口
outbound:
  create-order:
    host: ${BMS_INVENTORY_SIT_HOST:https://bms-inventory-sit.holderzone.cn}
inventory-service:
  intranet-host: ${BMS_INVENTORY_SIT_INTRANET_HOST:http://holder-fsgen-inventory.market:8384}